<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIHive.world | AI Education & Recruitment</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #FFFDF7;
        }
        .honeycomb {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 0l25.98 15v30L30 60 4.02 45V15L30 0z' fill-opacity='0.05' fill='%23FFC107' fill-rule='evenodd'/%3E%3C/svg%3E");
        }
        .hero-gradient {
            background: linear-gradient(135deg, rgba(255,193,7,0.2) 0%, rgba(255,255,255,0) 100%);
        }
        .btn-primary {
            background-color: #FFC107;
            color: #333;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #FFB300;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .btn-secondary {
            background-color: #fff;
            color: #333;
            border: 2px solid #FFC107;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background-color: #FFF8E1;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .card {
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .hexagon {
            clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
        }
        .hexagon-lg {
            clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
        }
        .hexagon-sm {
            clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
        }
        .hexagon-pulse {
            animation: pulse 3s infinite ease-in-out;
        }
        .hexagon-float {
            animation: float 5s infinite ease-in-out;
        }
        .hexagon-rotate {
            animation: rotate 8s infinite linear;
        }
        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.05); opacity: 1; }
            100% { transform: scale(1); opacity: 0.7; }
        }
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 100;
            overflow-y: auto;
        }
        .modal-content {
            position: relative;
            background-color: #fff;
            margin: 5% auto;
            padding: 20px;
            width: 90%;
            max-width: 600px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-height: 90vh;
            overflow-y: auto;
        }
        .close-button {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 24px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            z-index: 10;
        }
        .close-button:hover {
            color: #333;
        }
        .iframe-modal-content {
            position: relative;
            background-color: #fff;
            margin: 2% auto;
            padding: 0;
            width: 95%;
            max-width: 800px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-height: 95vh;
            overflow: hidden;
        }
        .iframe-container {
            width: 100%;
            height: 80vh;
            border: none;
            border-radius: 0 0 10px 10px;
        }
        .iframe-header {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .iframe-header h3 {
            margin: 0;
            color: #333;
            font-size: 1.2rem;
            font-weight: 600;
        }

    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="bg-white shadow-sm sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <svg class="h-8 w-8 text-amber-500" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 3L4 7.5V16.5L12 21L20 16.5V7.5L12 3ZM18 15.5L12 19L6 15.5V8.5L12 5L18 8.5V15.5Z"/>
                            <path d="M12 12.5L8 10V15L12 17.5L16 15V10L12 12.5Z" fill="#FFF8E1"/>
                        </svg>
                        <span class="ml-2 text-xl font-bold text-gray-800">AIHive<span class="text-amber-500">.world</span></span>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <a href="#" class="px-3 py-2 text-sm font-medium text-gray-700 hover:text-amber-500">Home</a>
                    <a href="#how-it-works" class="px-3 py-2 text-sm font-medium text-gray-700 hover:text-amber-500">How It Works</a>
                    <a href="#for-who" class="px-3 py-2 text-sm font-medium text-gray-700 hover:text-amber-500">For Who</a>
                    <button id="nav-get-started" class="btn-primary px-4 py-2 rounded-full text-sm font-medium">Get Started</button>
                </div>
                <div class="flex md:hidden items-center">
                    <button id="mobile-menu-button" class="p-2 rounded-md text-gray-700 hover:text-amber-500 focus:outline-none">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-md">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-amber-500">Home</a>
                <a href="#how-it-works" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-amber-500">How It Works</a>
                <a href="#for-who" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-amber-500">For Who</a>
                <button id="mobile-get-started" class="btn-primary w-full mt-2 px-4 py-2 rounded-full text-base font-medium">Get Started</button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-gradient honeycomb py-16 md:py-24 relative overflow-hidden">
        <!-- Enhanced Hexagon Background Elements -->
        <div class="absolute inset-0 z-0 overflow-hidden">
            <!-- Large background hexagons -->
            <div class="hexagon-lg bg-amber-100 w-64 h-64 opacity-20 absolute -top-20 -left-20 hexagon-pulse"></div>
            <div class="hexagon-lg bg-amber-200 w-48 h-48 opacity-30 absolute top-1/4 -right-10 hexagon-float"></div>
            <div class="hexagon-lg bg-amber-50 w-56 h-56 opacity-25 absolute bottom-0 left-1/4 hexagon-pulse" style="animation-delay: 1s;"></div>
            
            <!-- Small decorative hexagons -->
            <div class="hexagon-sm bg-amber-300 w-12 h-12 opacity-40 absolute top-1/3 left-1/4 hexagon-float" style="animation-delay: 0.5s;"></div>
            <div class="hexagon-sm bg-amber-400 w-8 h-8 opacity-30 absolute top-1/2 right-1/3 hexagon-float" style="animation-delay: 1.5s;"></div>
            <div class="hexagon-sm bg-amber-500 w-10 h-10 opacity-20 absolute bottom-1/4 right-1/4 hexagon-pulse" style="animation-delay: 2s;"></div>
            
            <!-- Tech-inspired elements -->
            <div class="absolute top-1/4 left-1/3">
                <svg class="w-16 h-16 text-amber-200 opacity-40 hexagon-rotate" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                </svg>
            </div>
            <div class="absolute bottom-1/3 right-1/3">
                <svg class="w-12 h-12 text-amber-300 opacity-30" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"></path>
                </svg>
            </div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-800 leading-tight">
                        Don't Get Left Behind in the AI Revolution
                    </h1>
                    <p class="mt-4 text-xl text-gray-600">
                        Join thousands who've already transformed their careers with AI. From complete beginner to landing your dream AI job - we'll guide you every step of the way.
                    </p>
                    <div class="mt-6 bg-amber-50 border-l-4 border-amber-400 p-4 rounded-r-lg">
                        <p class="text-amber-800 font-medium">
                            ⚡ Limited Time: Free AI Skills Assessment + Personalised Career Roadmap
                        </p>
                    </div>
                    <div class="mt-8 flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <button id="hero-get-started" class="btn-primary px-8 py-3 rounded-full text-base font-medium">
                            Get My Free Assessment Now
                        </button>
                        <button id="pipedrive-form-btn" class="btn-secondary px-8 py-3 rounded-full text-base font-medium">
                            Quick Contact Form
                        </button>
                        <button id="learn-more-btn" class="btn-secondary px-8 py-3 rounded-full text-base font-medium">
                            See Success Stories
                        </button>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative">
                        <!-- Enhanced hexagon grid visualization -->
                        <svg class="relative z-10 w-full max-w-md" viewBox="0 0 500 400" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <!-- Background elements -->
                            <circle cx="250" cy="200" r="120" fill="#FFF8E1" opacity="0.6" />
                            
                            <!-- Main Hexagon Grid -->
                            <g transform="translate(100, 50)">
                                <!-- Central Hexagons -->
                                <path d="M0 87L75 43.5L150 87V174L75 217.5L0 174V87Z" fill="#FFF8E1" stroke="#FFC107" stroke-width="2"/>
                                <path d="M150 87L225 43.5L300 87V174L225 217.5L150 174V87Z" fill="#FFF8E1" stroke="#FFC107" stroke-width="2"/>
                                
                                <!-- Top Row Hexagons -->
                                <path d="M75 -43.5L150 0L75 43.5L0 0L75 -43.5Z" fill="#FFF8E1" stroke="#FFC107" stroke-width="2"/>
                                <path d="M225 -43.5L300 0L225 43.5L150 0L225 -43.5Z" fill="#FFF8E1" stroke="#FFC107" stroke-width="2"/>
                                
                                <!-- Bottom Row Hexagons -->
                                <path d="M75 217.5L150 261L75 304.5L0 261L75 217.5Z" fill="#FFF8E1" stroke="#FFC107" stroke-width="2"/>
                                <path d="M225 217.5L300 261L225 304.5L150 261L225 217.5Z" fill="#FFF8E1" stroke="#FFC107" stroke-width="2"/>
                                
                                <!-- Additional Tech-Inspired Hexagons -->
                                <path d="M-75 43.5L0 0L75 43.5L0 87L-75 43.5Z" fill="#FFF8E1" stroke="#FFC107" stroke-width="2" opacity="0.7"/>
                                <path d="M300 87L375 43.5L450 87L375 130.5L300 87Z" fill="#FFF8E1" stroke="#FFC107" stroke-width="2" opacity="0.7"/>
                                <path d="M-75 130.5L0 87L75 130.5L0 174L-75 130.5Z" fill="#FFF8E1" stroke="#FFC107" stroke-width="2" opacity="0.7"/>
                                <path d="M300 174L375 130.5L450 174L375 217.5L300 174Z" fill="#FFF8E1" stroke="#FFC107" stroke-width="2" opacity="0.7"/>
                                
                                <!-- Connection Lines -->
                                <line x1="75" y1="43.5" x2="75" y2="217.5" stroke="#FFC107" stroke-width="1" stroke-dasharray="4 4" opacity="0.6"/>
                                <line x1="225" y1="43.5" x2="225" y2="217.5" stroke="#FFC107" stroke-width="1" stroke-dasharray="4 4" opacity="0.6"/>
                                <line x1="0" y1="87" x2="300" y2="87" stroke="#FFC107" stroke-width="1" stroke-dasharray="4 4" opacity="0.6"/>
                                <line x1="0" y1="174" x2="300" y2="174" stroke="#FFC107" stroke-width="1" stroke-dasharray="4 4" opacity="0.6"/>
                                
                                <!-- Data Points -->
                                <circle cx="75" cy="43.5" r="6" fill="#FFB300"/>
                                <circle cx="225" cy="43.5" r="6" fill="#FFB300"/>
                                <circle cx="75" cy="217.5" r="6" fill="#FFB300"/>
                                <circle cx="225" cy="217.5" r="6" fill="#FFB300"/>
                                <circle cx="0" cy="87" r="4" fill="#FFB300" opacity="0.8"/>
                                <circle cx="150" cy="87" r="4" fill="#FFB300" opacity="0.8"/>
                                <circle cx="300" cy="87" r="4" fill="#FFB300" opacity="0.8"/>
                                <circle cx="0" cy="174" r="4" fill="#FFB300" opacity="0.8"/>
                                <circle cx="150" cy="174" r="4" fill="#FFB300" opacity="0.8"/>
                                <circle cx="300" cy="174" r="4" fill="#FFB300" opacity="0.8"/>
                                
                                <!-- Icons -->
                                <g transform="translate(75, 87)" fill="#333">
                                    <path d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l4-2.18L12 3zm-1 13.5l-4-2.18v-4.36l4 2.18v4.36zm1-6.86L8 7.14 12 5l4 2.14-4 2.5z"/>
                                </g>
                                <g transform="translate(225, 87)" fill="#333">
                                    <path d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M20 6h-4V4c0-1.1-.9-2-2-2h-4c-1.1 0-2 .9-2 2v2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-8 12c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z"/>
                                    <path d="M12 12c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1z"/>
                                </g>
                                
                                <!-- AI/ML Symbols -->
                                <g transform="translate(75, 217.5)" fill="#333">
                                    <path d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M15 9H9v6h6V9zm-2 4h-2v-2h2v2zm8-2V9h-2V7c0-1.1-.9-2-2-2h-2V3h-2v2h-2V3H9v2H7c-1.1 0-2 .9-2 2v2H3v2h2v2H3v2h2v2c0 1.1.9 2 2 2h2v2h2v-2h2v2h2v-2h2c1.1 0 2-.9 2-2v-2h2v-2h-2v-2h2zm-4 6H7V7h10v10z"/>
                                </g>
                                <g transform="translate(225, 217.5)" fill="#333">
                                    <path d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14z"/>
                                    <path d="M7 12h2v5h-2zm4-7h2v12h-2zm4 4h2v8h-2z"/>
                                </g>
                            </g>
                            
                            <!-- Binary/Data Flow Elements -->
                            <g opacity="0.4">
                                <text x="120" y="70" font-family="monospace" font-size="8" fill="#FFC107">01001</text>
                                <text x="340" y="90" font-family="monospace" font-size="8" fill="#FFC107">10110</text>
                                <text x="180" y="320" font-family="monospace" font-size="8" fill="#FFC107">11001</text>
                                <text x="280" y="280" font-family="monospace" font-size="8" fill="#FFC107">01010</text>
                            </g>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-800">Your Journey to AI Success Starts Here</h2>
                <p class="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
                    Stop wondering "what if" and start your transformation today. Here's exactly how we'll get you there:
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="card bg-white rounded-xl shadow-md p-6 border border-gray-100">
                    <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">1. Discover Your Potential</h3>
                    <p class="text-gray-600">
                        Uncover your hidden AI talents with our comprehensive assessment. Finally know exactly where you stand and what's possible for your future.
                    </p>
                </div>
                
                <div class="card bg-white rounded-xl shadow-md p-6 border border-gray-100">
                    <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">2. Fast-Track Your Learning</h3>
                    <p class="text-gray-600">
                        Skip the guesswork. Get a personalised roadmap with the exact courses and resources you need to succeed - no time wasted on irrelevant content.
                    </p>
                </div>
                
                <div class="card bg-white rounded-xl shadow-md p-6 border border-gray-100">
                    <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">3. Prove Your Worth</h3>
                    <p class="text-gray-600">
                        Build an impressive portfolio with real-world AI challenges. Show employers you're not just another candidate - you're the solution they need.
                    </p>
                </div>
                
                <div class="card bg-white rounded-xl shadow-md p-6 border border-gray-100">
                    <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">4. Land Your Dream Job</h3>
                    <p class="text-gray-600">
                        Get direct access to exclusive AI job opportunities. Our partner companies are actively seeking talent just like you - bypass the competition.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- For Who Section -->
    <section id="for-who" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-800">Which Path Will Transform Your Future?</h2>
                <p class="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
                    Choose your journey and join the AI revolution. Thousands have already started - don't wait until it's too late.
                </p>
                <div class="mt-6 bg-red-50 border border-red-200 rounded-lg p-4 max-w-2xl mx-auto">
                    <p class="text-red-700 font-medium">
                        🚨 AI jobs are growing 74% year-over-year. The question isn't if you'll need AI skills - it's when.
                    </p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="h-3 bg-amber-400"></div>
                    <div class="p-6">
                        <div class="w-16 h-16 bg-amber-100 hexagon flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2 text-center">Career Changers & Professionals</h3>
                        <p class="text-gray-600 text-center mb-4">
                            Ready to future-proof your career? From complete beginner to AI expert - we'll show you exactly how to get there.
                        </p>
                        <div class="text-center mb-6">
                            <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                                Average salary increase: £15,000+
                            </span>
                        </div>
                        <button class="btn-primary w-full py-2 rounded-lg text-base font-medium open-modal" data-modal="individual-modal">
                            Start My Transformation
                        </button>
                    </div>
                </div>
                
                <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="h-3 bg-amber-500"></div>
                    <div class="p-6">
                        <div class="w-16 h-16 bg-amber-100 hexagon flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4zm3 1h6v4H7V5zm8 8v2h1v1H4v-1h1v-2a1 1 0 011-1h8a1 1 0 011 1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2 text-center">Forward-Thinking Businesses</h3>
                        <p class="text-gray-600 text-center mb-4">
                            Don't let competitors steal your market share. Build an AI-powered workforce that drives real results and revenue growth.
                        </p>
                        <div class="text-center mb-6">
                            <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                                ROI: 300%+ within 12 months
                            </span>
                        </div>
                        <button class="btn-primary w-full py-2 rounded-lg text-base font-medium open-modal" data-modal="business-modal">
                            Secure My Competitive Edge
                        </button>
                    </div>
                </div>
                
                <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="h-3 bg-amber-600"></div>
                    <div class="p-6">
                        <div class="w-16 h-16 bg-amber-100 hexagon flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2 text-center">Education & Training Providers</h3>
                        <p class="text-gray-600 text-center mb-4">
                            Tap into the fastest-growing education market. Connect with motivated learners who are ready to invest in their AI future.
                        </p>
                        <div class="text-center mb-6">
                            <span class="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full font-medium">
                                Market size: £2.3B+ and growing
                            </span>
                        </div>
                        <button class="btn-primary w-full py-2 rounded-lg text-base font-medium open-modal" data-modal="provider-modal">
                            Expand My Reach
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-800">Why Thousands Choose AIHive.world</h2>
                <p class="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
                    Stop wasting time on generic courses that lead nowhere. Here's what makes us different:
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-8 h-8 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2 text-center">No More Guesswork</h3>
                    <p class="text-gray-600 text-center">
                        Everything you need in one place - from assessment to job placement. No jumping between platforms or wondering what to do next.
                    </p>
                </div>
                
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-8 h-8 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2 text-center">Skip The Fluff</h3>
                    <p class="text-gray-600 text-center">
                        Learn only what you need, when you need it. Our AI-powered recommendations cut through the noise to focus on skills that actually matter.
                    </p>
                </div>
                
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-8 h-8 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2 text-center">Prove Your Value</h3>
                    <p class="text-gray-600 text-center">
                        Build a portfolio that makes employers say "we need this person." Real projects, real results, real job offers.
                    </p>
                </div>
                
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-8 h-8 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2 text-center">Fast-Track To Success</h3>
                    <p class="text-gray-600 text-center">
                        Why spend years figuring it out alone? Get direct access to opportunities that others don't even know exist.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="py-16 bg-gradient-to-r from-amber-400 to-amber-600">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                The AI Revolution Won't Wait For You
            </h2>
            <p class="text-xl text-amber-100 mb-8 max-w-2xl mx-auto">
                Every day you delay is another day your competitors get ahead. Join the thousands who've already started their AI transformation.
            </p>
            <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8 max-w-2xl mx-auto">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-white">
                    <div>
                        <div class="text-2xl font-bold">5,000+</div>
                        <div class="text-amber-100">Careers Transformed</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold">£18,500</div>
                        <div class="text-amber-100">Average Salary Increase</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold">92%</div>
                        <div class="text-amber-100">Job Placement Rate</div>
                    </div>
                </div>
            </div>
            <button id="final-cta" class="bg-white text-amber-600 px-8 py-4 rounded-full text-lg font-bold hover:bg-amber-50 transition-all duration-300 transform hover:scale-105 shadow-lg">
                Start My Free Assessment Now
            </button>
            <p class="text-amber-100 mt-4 text-sm">
                ⏰ Limited time offer - Free personalised career roadmap included
            </p>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <div class="flex items-center">
                        <svg class="h-8 w-8 text-amber-400" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 3L4 7.5V16.5L12 21L20 16.5V7.5L12 3ZM18 15.5L12 19L6 15.5V8.5L12 5L18 8.5V15.5Z"/>
                            <path d="M12 12.5L8 10V15L12 17.5L16 15V10L12 12.5Z" fill="#FFF8E1"/>
                        </svg>
                        <span class="ml-2 text-xl font-bold">AIHive<span class="text-amber-400">.world</span></span>
                    </div>
                    <p class="mt-4 text-gray-400">
                        Transforming AI education and recruitment through comprehensive assessment, 
                        personalised learning, and practical skill demonstration.
                    </p>
                    <div class="mt-6 flex space-x-4">
                        <a href="https://www.linkedin.com/company/onehive.ai/" target="_blank" class="text-gray-400 hover:text-amber-400">
                            <span class="sr-only">LinkedIn</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 uppercase tracking-wider">Resources</h3>
                    <ul class="mt-4 space-y-2">
                        <li><a href="#" class="text-base text-gray-300 hover:text-amber-400">About Us</a></li>
                        <li><a href="#how-it-works" class="text-base text-gray-300 hover:text-amber-400">How It Works</a></li>
                        <li><a href="https://onehive.ai" target="_blank" class="text-base text-gray-300 hover:text-amber-400">OneHive</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 uppercase tracking-wider">Legal</h3>
                    <ul class="mt-4 space-y-2">
                        <li><a href="https://onehive.ai/privacy-policy" target="_blank" class="text-base text-gray-300 hover:text-amber-400">Privacy Policy</a></li>
                        <li><a href="https://onehive.ai/terms-conditions" target="_blank" class="text-base text-gray-300 hover:text-amber-400">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-12 border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-base text-gray-400">
                    &copy; 2023 AIHive.world. All rights reserved.
                </p>
                <p class="text-base text-gray-400 mt-4 md:mt-0">
                    Powered by <a href="https://onehive.ai" target="_blank" class="text-amber-400 hover:text-amber-300">OneHive</a>
                </p>
            </div>
        </div>
    </footer>

    <!-- Modal for Individual Form -->
    <div id="individual-modal" class="modal">
        <div class="modal-content">
            <span class="close-button" data-modal="individual-modal">&times;</span>
            <div class="p-6">
                <h3 class="text-2xl font-bold text-gray-800 mb-2 text-center">Start Your AI Transformation</h3>
                <p class="text-gray-600 text-center mb-6">Get your free assessment and personalised career roadmap</p>
                <form action="https://webforms.pipedrive.com/f/cs3leacE9BHKPJvImZTfQm2VTsCKlA0tzU9TfvDACtInhf528x6sQS1zAtgyTlCUo3" method="POST" class="space-y-4">
                    <div>
                        <label for="individual-name" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                        <input type="text" id="individual-name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <div>
                        <label for="individual-email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                        <input type="email" id="individual-email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <div>
                        <label for="individual-role" class="block text-sm font-medium text-gray-700 mb-1">Current Role</label>
                        <input type="text" id="individual-role" name="role" placeholder="e.g. Marketing Manager, Software Developer" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <div>
                        <label for="individual-company" class="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                        <input type="text" id="individual-company" name="company" placeholder="Your current company" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <button type="submit" class="w-full btn-primary py-3 rounded-lg text-base font-medium mt-6">
                        Get My Free Assessment
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for Business Form -->
    <div id="business-modal" class="modal">
        <div class="modal-content">
            <span class="close-button" data-modal="business-modal">&times;</span>
            <div class="p-6">
                <h3 class="text-2xl font-bold text-gray-800 mb-2 text-center">Partner With Us</h3>
                <p class="text-gray-600 text-center mb-6">Build your AI-powered workforce and stay ahead of the competition</p>
                <form action="https://webforms.pipedrive.com/f/6iwSkQ8IxGvGumblhS46UWq9HlKpSGQUAzGs0n8A9CiHGrbFIkMaSd5EyxsP86yDnR" method="POST" class="space-y-4">
                    <div>
                        <label for="business-name" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                        <input type="text" id="business-name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <div>
                        <label for="business-email" class="block text-sm font-medium text-gray-700 mb-1">Business Email *</label>
                        <input type="email" id="business-email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <div>
                        <label for="business-role" class="block text-sm font-medium text-gray-700 mb-1">Your Role *</label>
                        <input type="text" id="business-role" name="role" required placeholder="e.g. CEO, HR Director, CTO" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <div>
                        <label for="business-company" class="block text-sm font-medium text-gray-700 mb-1">Company Name *</label>
                        <input type="text" id="business-company" name="company" required placeholder="Your company name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <button type="submit" class="w-full btn-primary py-3 rounded-lg text-base font-medium mt-6">
                        Schedule Partnership Discussion
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for Provider Form -->
    <div id="provider-modal" class="modal">
        <div class="modal-content">
            <span class="close-button" data-modal="provider-modal">&times;</span>
            <div class="p-6">
                <h3 class="text-2xl font-bold text-gray-800 mb-2 text-center">Join Our Network</h3>
                <p class="text-gray-600 text-center mb-6">Connect with motivated learners ready to invest in AI education</p>
                <form action="https://webforms.pipedrive.com/f/6N6tNrdek39yRMhiKHKPCO2ZkmIMhxtABWu0bwNexx8TwGPkR3MgIoPtbPmCBjg4MP" method="POST" class="space-y-4">
                    <div>
                        <label for="provider-name" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                        <input type="text" id="provider-name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <div>
                        <label for="provider-email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                        <input type="email" id="provider-email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <div>
                        <label for="provider-role" class="block text-sm font-medium text-gray-700 mb-1">Your Role *</label>
                        <input type="text" id="provider-role" name="role" required placeholder="e.g. Course Creator, Training Manager" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <div>
                        <label for="provider-company" class="block text-sm font-medium text-gray-700 mb-1">Organization Name *</label>
                        <input type="text" id="provider-company" name="company" required placeholder="Your training organization" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                    </div>
                    <button type="submit" class="w-full btn-primary py-3 rounded-lg text-base font-medium mt-6">
                        Join Provider Network
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Pipedrive Iframe Modal -->
    <div id="pipedrive-iframe-modal" class="modal">
        <div class="iframe-modal-content">
            <div class="iframe-header">
                <h3>Contact Us</h3>
                <span class="close-button" data-modal="pipedrive-iframe-modal">&times;</span>
            </div>
            <iframe
                id="pipedrive-iframe"
                class="iframe-container"
                src="https://webforms.pipedrive.com/f/cs3leacE9BHKPJvImZTfQm2VTsCKlA0tzU9TfvDACtInhf528x6sQS1zAtgyTlCUo3"
                frameborder="0"
                allowfullscreen>
            </iframe>
        </div>
    </div>

    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Get Started buttons scroll to For Who section
        document.getElementById('nav-get-started').addEventListener('click', () => {
            document.getElementById('for-who').scrollIntoView({ behavior: 'smooth' });
        });
        
        document.getElementById('mobile-get-started').addEventListener('click', () => {
            document.getElementById('for-who').scrollIntoView({ behavior: 'smooth' });
        });
        
        document.getElementById('hero-get-started').addEventListener('click', () => {
            document.getElementById('for-who').scrollIntoView({ behavior: 'smooth' });
        });
        
        // Learn More button scrolls to How It Works section
        document.getElementById('learn-more-btn').addEventListener('click', () => {
            document.getElementById('how-it-works').scrollIntoView({ behavior: 'smooth' });
        });
        
        // Final CTA button scrolls to For Who section
        document.getElementById('final-cta').addEventListener('click', () => {
            document.getElementById('for-who').scrollIntoView({ behavior: 'smooth' });
        });

        // Pipedrive iframe modal button
        document.getElementById('pipedrive-form-btn').addEventListener('click', () => {
            document.getElementById('pipedrive-iframe-modal').style.display = 'block';
            document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
        });
        
        // Modal functionality
        const openModalButtons = document.querySelectorAll('.open-modal');
        const closeButtons = document.querySelectorAll('.close-button');
        const modals = document.querySelectorAll('.modal');
        
        // Open modal
        openModalButtons.forEach(button => {
            button.addEventListener('click', () => {
                const modalId = button.getAttribute('data-modal');
                document.getElementById(modalId).style.display = 'block';
                document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
            });
        });
        
        // Close modal when clicking the close button
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                const modalId = button.getAttribute('data-modal');
                document.getElementById(modalId).style.display = 'none';
                document.body.style.overflow = 'auto'; // Re-enable scrolling
            });
        });
        
        // Close modal when clicking outside the modal content
        window.addEventListener('click', (event) => {
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto'; // Re-enable scrolling
                }
            });
        });
        

    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'96a785a485af249d',t:'MTc1NDQwOTc4MC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
